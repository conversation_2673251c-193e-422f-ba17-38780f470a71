package v1

import "github.com/lib/pq"

type SysUserCreateParams struct {
	Username string        `json:"username" binding:"required,max=64" example:"admin"`
	Password string        `json:"password" example:"123456"`
	Nickname string        `json:"nickname" binding:"max=64" example:"管理员"`
	Gender   uint          `json:"gender" example:"0"`
	Phone    string        `json:"phone" example:"13800138000"`
	Email    string        `json:"email" binding:"omitempty,email" example:"<EMAIL>"`
	Avatar   string        `json:"avatar" example:"https://example.com/avatar.png"`
	Status   bool          `json:"status" example:"true"`
	RoleIds  pq.Int64Array `json:"roleIds" example:"1,2,3"`
	TenantId uint          `json:"tenantId" example:"1"`
}

type SysUserUpdateParams struct {
	SysUserCreateParams
}

type SysUserResponse struct {
	ID        uint          `json:"id"`
	UserId    string        `json:"userId"`
	Username  string        `json:"username"`
	Nickname  string        `json:"nickname"`
	Gender    uint          `json:"gender"`
	Phone     string        `json:"phone"`
	Email     string        `json:"email"`
	Avatar    string        `json:"avatar"`
	Status    bool          `json:"status"`
	RoleIds   pq.Int64Array `json:"roleIds"`
	TenantId  uint          `json:"tenantId"`
	CreatedAt string        `json:"createdAt"`
	UpdatedAt string        `json:"updatedAt"`
}
