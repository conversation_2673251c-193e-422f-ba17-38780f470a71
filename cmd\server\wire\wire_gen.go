// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"daisy-server/internal/handler"
	"daisy-server/internal/job"
	"daisy-server/internal/repository"
	"daisy-server/internal/server"
	"daisy-server/internal/service"
	"daisy-server/pkg/app"
	"daisy-server/pkg/jwt"
	"daisy-server/pkg/log"
	"daisy-server/pkg/rbac"
	"daisy-server/pkg/server/http"
	"daisy-server/pkg/sid"
	"github.com/google/wire"
	"github.com/spf13/viper"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	db := repository.NewDB(viperViper, logger)
	repositoryRepository := repository.NewRepository(logger, db)
	client := repository.NewRedis(viperViper)
	rbacRBAC, err := rbac.NewRBAC(viperViper, repositoryRepository, logger, client)
	if err != nil {
		return nil, nil, err
	}
	sysLogRepository := repository.NewSysLogRepository(repositoryRepository)
	handlerHandler := handler.NewHandler(logger)
	transaction := repository.NewTransaction(repositoryRepository)
	sidSid := sid.NewSid()
	serviceService := service.NewService(transaction, logger, sidSid, jwtJWT)
	authService := service.NewAuthService(serviceService)
	sysMenuRepository := repository.NewSysMenuRepository(repositoryRepository)
	sysMenuService := service.NewSysMenuService(serviceService, sysMenuRepository)
	authHandler := handler.NewAuthHandler(handlerHandler, authService, sysMenuService)
	uploadService := service.NewUploadService(serviceService, viperViper)
	uploadHandler := handler.NewUploadHandler(handlerHandler, uploadService)
	sysUserRepository := repository.NewSysUserRepository(repositoryRepository)
	sysTenantRepository := repository.NewSysTenantRepository(repositoryRepository)
	sysUserService := service.NewSysUserService(serviceService, sysUserRepository, sysTenantRepository)
	sysUserHandler := handler.NewSysUserHandler(handlerHandler, sysUserService)
	sysRoleRepository := repository.NewSysRoleRepository(repositoryRepository)
	sysRoleService := service.NewSysRoleService(serviceService, sysRoleRepository)
	sysRoleHandler := handler.NewSysRoleHandler(handlerHandler, sysRoleService)
	sysDeptRepository := repository.NewSysDeptRepository(repositoryRepository)
	sysDeptService := service.NewSysDeptService(serviceService, sysDeptRepository)
	sysDeptHandler := handler.NewSysDeptHandler(handlerHandler, sysDeptService)
	sysMenuHandler := handler.NewSysMenuHandler(handlerHandler, sysMenuService)
	sysLogService := service.NewSysLogService(serviceService, sysLogRepository)
	sysLogHandler := handler.NewSysLogHandler(handlerHandler, sysLogService)
	sysApiRepository := repository.NewSysApiRepository(repositoryRepository)
	sysApiService := service.NewSysApiService(serviceService, sysApiRepository)
	sysApiHandler := handler.NewSysApiHandler(handlerHandler, sysApiService, viperViper)
	sysDictRepository := repository.NewSysDictRepository(repositoryRepository)
	sysDictService := service.NewSysDictService(serviceService, sysDictRepository)
	sysDictHandler := handler.NewSysDictHandler(handlerHandler, sysDictService)
	sysConfigRepository := repository.NewSysConfigRepository(repositoryRepository)
	sysConfigService := service.NewSysConfigService(serviceService, sysConfigRepository)
	sysConfigHandler := handler.NewSysConfigHandler(handlerHandler, sysConfigService)
	sysTenantService := service.NewSysTenantService(serviceService, sysTenantRepository)
	sysTenantHandler := handler.NewSysTenantHandler(handlerHandler, sysTenantService)
	cmsMetaRepository := repository.NewCmsMetaRepository(repositoryRepository)
	cmsMetaService := service.NewCmsMetaService(serviceService, cmsMetaRepository)
	cmsMetaHandler := handler.NewCmsMetaHandler(handlerHandler, cmsMetaService)
	cmsPostRepository := repository.NewCmsPostRepository(repositoryRepository)
	cmsPostService := service.NewCmsPostService(serviceService, cmsPostRepository)
	cmsPostHandler := handler.NewCmsPostHandler(handlerHandler, cmsPostService)
	businessCompanyRepository := repository.NewBusinessCompanyRepository(repositoryRepository)
	businessCompanyService := service.NewBusinessCompanyService(serviceService, businessCompanyRepository)
	businessCompanyHandler := handler.NewBusinessCompanyHandler(handlerHandler, businessCompanyService)
	businessProductRepository := repository.NewBusinessProductRepository(repositoryRepository)
	businessProductService := service.NewBusinessProductService(serviceService, businessProductRepository, businessCompanyRepository)
	businessProductHandler := handler.NewBusinessProductHandler(handlerHandler, businessProductService)
	businessTalentRepository := repository.NewBusinessTalentRepository(repositoryRepository)
	businessTalentService := service.NewBusinessTalentService(serviceService, businessTalentRepository)
	businessTalentHandler := handler.NewBusinessTalentHandler(handlerHandler, businessTalentService)
	businessExpertRepository := repository.NewBusinessExpertRepository(repositoryRepository)
	businessExpertService := service.NewBusinessExpertService(serviceService, businessExpertRepository)
	businessExpertHandler := handler.NewBusinessExpertHandler(handlerHandler, businessExpertService)
	businessKnowledgeRepository := repository.NewBusinessKnowledgeRepository(repositoryRepository)
	businessKnowledgeService := service.NewBusinessKnowledgeService(serviceService, businessKnowledgeRepository)
	businessKnowledgeHandler := handler.NewBusinessKnowledgeHandler(handlerHandler, businessKnowledgeService)
	businessAppRepository := repository.NewBusinessAppRepository(repositoryRepository)
	businessAppService := service.NewBusinessAppService(serviceService, businessAppRepository)
	businessAppHandler := handler.NewBusinessAppHandler(handlerHandler, businessAppService)
	businessReportRepository := repository.NewBusinessReportRepository(repositoryRepository)
	businessReportService := service.NewBusinessReportService(serviceService, businessReportRepository, businessCompanyRepository)
	businessReportHandler := handler.NewBusinessReportHandler(handlerHandler, businessReportService)
	businessJobRepository := repository.NewBusinessJobRepository(repositoryRepository)
	businessJobService := service.NewBusinessJobService(serviceService, businessJobRepository, businessCompanyRepository)
	businessJobHandler := handler.NewBusinessJobHandler(handlerHandler, businessJobService)
	httpServer := server.NewHTTPServer(logger, viperViper, jwtJWT, rbacRBAC, sysLogRepository, authHandler, uploadHandler, sysUserHandler, sysRoleHandler, sysDeptHandler, sysMenuHandler, sysLogHandler, sysApiHandler, sysDictHandler, sysConfigHandler, sysTenantHandler, cmsMetaHandler, cmsPostHandler, businessCompanyHandler, businessProductHandler, businessTalentHandler, businessExpertHandler, businessKnowledgeHandler, businessAppHandler, businessReportHandler, businessJobHandler)
	jobJob := job.NewJob(transaction, logger, sidSid)
	userJob := job.NewUserJob(jobJob, sysUserRepository)
	jobServer := server.NewJobServer(logger, userJob)
	appApp := newApp(httpServer, jobServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewTransaction, repository.NewSysUserRepository, repository.NewSysRoleRepository, repository.NewSysDeptRepository, repository.NewSysMenuRepository, repository.NewSysLogRepository, repository.NewSysApiRepository, repository.NewSysDictRepository, repository.NewSysConfigRepository, repository.NewSysTenantRepository, repository.NewCmsMetaRepository, repository.NewCmsPostRepository, repository.NewBusinessCompanyRepository, repository.NewBusinessProductRepository, repository.NewBusinessTalentRepository, repository.NewBusinessExpertRepository, repository.NewBusinessKnowledgeRepository, repository.NewBusinessAppRepository, repository.NewBusinessReportRepository, repository.NewBusinessJobRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewAuthService, service.NewUploadService, service.NewSysUserService, service.NewSysRoleService, service.NewSysDeptService, service.NewSysMenuService, service.NewSysLogService, service.NewSysApiService, service.NewSysDictService, service.NewSysConfigService, service.NewSysTenantService, service.NewCmsMetaService, service.NewCmsPostService, service.NewBusinessCompanyService, service.NewBusinessProductService, service.NewBusinessTalentService, service.NewBusinessExpertService, service.NewBusinessKnowledgeService, service.NewBusinessAppService, service.NewBusinessReportService, service.NewBusinessJobService)

var handlerSet = wire.NewSet(handler.NewHandler, handler.NewAuthHandler, handler.NewUploadHandler, handler.NewSysUserHandler, handler.NewSysRoleHandler, handler.NewSysDeptHandler, handler.NewSysMenuHandler, handler.NewSysLogHandler, handler.NewSysApiHandler, handler.NewSysDictHandler, handler.NewSysConfigHandler, handler.NewSysTenantHandler, handler.NewCmsMetaHandler, handler.NewCmsPostHandler, handler.NewBusinessCompanyHandler, handler.NewBusinessProductHandler, handler.NewBusinessTalentHandler, handler.NewBusinessExpertHandler, handler.NewBusinessKnowledgeHandler, handler.NewBusinessAppHandler, handler.NewBusinessReportHandler, handler.NewBusinessJobHandler)

var jobSet = wire.NewSet(job.NewJob, job.NewUserJob)

var serverSet = wire.NewSet(server.NewHTTPServer, server.NewJobServer)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,

) *app.App {
	return app.NewApp(app.WithServer(httpServer, jobServer), app.WithName("daisy-server"))
}
