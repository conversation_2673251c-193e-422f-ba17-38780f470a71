package v1

type SysDeptCreateParams struct {
	Name     string `json:"name" binding:"required,max=64" example:"技术部"`
	Code     string `json:"code" binding:"required,max=64" example:"tech"`
	Summary  string `json:"summary" example:"负责技术开发"`
	Order    int    `json:"order" example:"1"`
	Status   bool   `json:"status" example:"true"`
	ParentId uint   `json:"parentId" example:"0"`
}

type SysDeptUpdateParams struct {
	SysDeptCreateParams
}

type SysDeptResponse struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Code      string `json:"code"`
	Summary   string `json:"summary"`
	Order     int    `json:"order"`
	Status    bool   `json:"status"`
	ParentId  uint   `json:"parentId"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
