package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessTalentCreateParams struct {
	Name    string             `json:"name" binding:"required,max=64" example:"张三"`
	Gender  uint               `json:"gender" binding:"required" example:"1"`
	Age     uint               `json:"age" example:"28"`
	Edu     uint               `json:"edu" binding:"required" example:"4"`
	Exp     uint               `json:"exp" example:"5"`
	Type    uint               `json:"type" binding:"required" example:"1"`
	Skills  datatypes.JSON     `json:"skills" swaggertype:"array,string" example:"Java,Go,Python"`
	Area    string             `json:"area" example:"上海"`
	Phone   string             `json:"phone" example:"***********"`
	Email   string             `json:"email" binding:"omitempty,email" example:"<EMAIL>"`
	Avatar  string             `json:"avatar" example:"https://example.com/avatar.jpg"`
	Address string             `json:"address" example:"上海市浦东新区张江高科技园区"`
	Summary string             `json:"summary" example:"人才简介"`
	Detail  string             `json:"detail" example:"个人详细介绍"`
	Party   bool               `json:"party" example:"true"`
	Files   []UploadFileParams `json:"files"`
	Order   int                `json:"order" example:"1"`
	Flag    pq.Int64Array      `json:"flag" example:"1,2"`
	Status  bool               `json:"status" example:"true"`
}

type BusinessTalentUpdateParams struct {
	BusinessTalentCreateParams
}

type BusinessTalentResponse struct {
	ID        uint               `json:"id"`
	Name      string             `json:"name"`
	Gender    uint               `json:"gender"`
	Age       uint               `json:"age"`
	Edu       uint               `json:"edu"`
	Exp       uint               `json:"exp"`
	Type      uint               `json:"type"`
	Skills    datatypes.JSON     `json:"skills"`
	Area      string             `json:"area"`
	Phone     string             `json:"phone"`
	Email     string             `json:"email"`
	Avatar    string             `json:"avatar"`
	Address   string             `json:"address"`
	Summary   string             `json:"summary"`
	Detail    string             `json:"detail"`
	Party     bool               `json:"party"`
	Files     []UploadFileParams `json:"files"`
	Order     int                `json:"order"`
	Flag      pq.Int64Array      `json:"flag"`
	Status    bool               `json:"status"`
	CreatedAt string             `json:"createdAt"`
	UpdatedAt string             `json:"updatedAt"`
}
