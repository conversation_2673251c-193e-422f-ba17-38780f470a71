package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessExpertCreateParams struct {
	Name    string         `json:"name" binding:"required,max=64" example:"张三"`
	Gender  uint           `json:"gender" binding:"required" example:"1"`
	Age     uint           `json:"age" example:"45"`
	Edu     uint           `json:"edu" binding:"required" example:"4"`
	Exp     uint           `json:"exp" example:"20"`
	Type    pq.Int64Array  `json:"type" binding:"required" example:"1,2,3"`
	Skills  datatypes.JSON `json:"skills" swaggertype:"array,string" example:"机械设计,自动化,控制系统"`
	Area    string         `json:"area" example:"北京"`
	Phone   string         `json:"phone" example:"***********"`
	Email   string         `json:"email" binding:"omitempty,email" example:"<EMAIL>"`
	Avatar  string         `json:"avatar" example:"https://example.com/avatar.jpg"`
	Tags    datatypes.JSON `json:"tags" swaggertype:"array,string" example:"高级,首席"`
	Summary string         `json:"summary" example:"专家简介"`
	Detail  string         `json:"detail" example:"专家详细介绍"`
	Party   bool           `json:"party" example:"true"`
	Order   int            `json:"order" example:"1"`
	Flag    pq.Int64Array  `json:"flag" example:"1,2"`
	Status  bool           `json:"status" example:"true"`
}

type BusinessExpertUpdateParams struct {
	BusinessExpertCreateParams
}

type BusinessExpertResponse struct {
	ID        uint           `json:"id"`
	Name      string         `json:"name"`
	Gender    uint           `json:"gender"`
	Age       uint           `json:"age"`
	Edu       uint           `json:"edu"`
	Exp       uint           `json:"exp"`
	Type      pq.Int64Array  `json:"type"`
	Skills    datatypes.JSON `json:"skills"`
	Area      string         `json:"area"`
	Phone     string         `json:"phone"`
	Email     string         `json:"email"`
	Avatar    string         `json:"avatar"`
	Tags      datatypes.JSON `json:"tags"`
	Summary   string         `json:"summary"`
	Detail    string         `json:"detail"`
	Party     bool           `json:"party"`
	Order     int            `json:"order"`
	Flag      pq.Int64Array  `json:"flag"`
	Status    bool           `json:"status"`
	CreatedAt string         `json:"createdAt"`
	UpdatedAt string         `json:"updatedAt"`
}
