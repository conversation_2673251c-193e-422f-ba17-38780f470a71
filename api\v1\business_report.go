package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessReportCreateParams struct {
	CompanyId uint               `json:"companyId" binding:"required" example:"1"`
	Title     string             `json:"title" binding:"required,max=255" example:"报告标题"`
	Summary   string             `json:"summary" example:"报告摘要"`
	Content   string             `json:"content" example:"报告内容"`
	Tags      datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Cover     string             `json:"cover" example:"https://example.com/cover.jpg"`
	Files     []UploadFileParams `json:"files"`
	Order     int                `json:"order" example:"1"`
	Flag      pq.Int64Array      `json:"flag" example:"1,2"`
	Status    bool               `json:"status" example:"true"`
}

type BusinessReportUpdateParams struct {
	BusinessReportCreateParams
}

type BusinessReportResponse struct {
	ID        uint                     `json:"id"`
	CompanyId uint                     `json:"companyId"`
	Title     string                   `json:"title"`
	Summary   string                   `json:"summary"`
	Content   string                   `json:"content"`
	Tags      datatypes.JSON           `json:"tags"`
	Cover     string                   `json:"cover"`
	Files     []UploadFileParams       `json:"files"`
	Order     int                      `json:"order"`
	Flag      pq.Int64Array            `json:"flag"`
	Status    bool                     `json:"status"`
	Company   *BusinessCompanyResponse `json:"company,omitempty"`
	CreatedAt string                   `json:"createdAt"`
	UpdatedAt string                   `json:"updatedAt"`
}
