package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"golang.org/x/crypto/bcrypt"
)

type SysUserService interface {
	Create(ctx context.Context, req *v1.SysUserCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysUserUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysUserResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysUserService(
	service *Service,
	sysUserRepository repository.SysUserRepository,
) SysUserService {
	return &sysUserService{
		Service:           service,
		sysUserRepository: sysUserRepository,
	}
}

type sysUserService struct {
	*Service
	sysUserRepository repository.SysUserRepository
}

// 用户相关方法实现
func (s *sysUserService) Create(ctx context.Context, req *v1.SysUserCreateParams) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 生成用户ID
	userId, err := s.sid.GenString()
	if err != nil {
		return err
	}

	user := &model.SysUser{}
	if err := copier.Copy(user, req); err != nil {
		return err
	}

	user.UserId = userId
	user.Password = string(hashedPassword)

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Create(ctx, user); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Update(ctx context.Context, id uint, req *v1.SysUserUpdateParams) error {
	user, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(user, req); err != nil {
		return err
	}

	// 修改密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		user.Password = string(hashedPassword)
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Update(ctx, user); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysUserRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysUserService) Get(ctx context.Context, id uint) (*v1.SysUserResponse, error) {
	user, err := s.sysUserRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysUserResponse{}
	if err := copier.Copy(response, user); err != nil {
		return nil, err
	}

	response.CreatedAt = user.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = user.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *sysUserService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 全文索引
	q := ctx.(*gin.Context).DefaultQuery("q", "")
	if q != "" {
		params.Query = q
	}

	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 用户名筛选
	username := ctx.(*gin.Context).DefaultQuery("username", "")
	if username != "" {
		params.AddFilter("username_like", username)
	}

	// 昵称筛选
	nickname := ctx.(*gin.Context).DefaultQuery("nickname", "")
	if nickname != "" {
		params.AddFilter("nickname_like", nickname)
	}

	// 手机号筛选
	phone := ctx.(*gin.Context).DefaultQuery("phone", "")
	if phone != "" {
		params.AddFilter("phone", phone)
	}

	// 邮箱筛选
	email := ctx.(*gin.Context).DefaultQuery("email", "")
	if email != "" {
		params.AddFilter("email", email)
	}

	// 租户筛选
	tenantId := ctx.(*gin.Context).DefaultQuery("tenantId", "")
	if tenantId != "" {
		params.AddFilter("tenant_id", tenantId)
	}

	users, total, err := s.sysUserRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysUserResponse, 0, len(users))
	for _, user := range users {
		response := &v1.SysUserResponse{}
		if err := copier.Copy(response, user); err != nil {
			return nil, err
		}

		response.CreatedAt = user.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = user.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
