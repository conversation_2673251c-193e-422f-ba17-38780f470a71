package server

import (
	"context"
	"daisy-server/internal/model"
	"daisy-server/pkg/log"
	"daisy-server/pkg/sid"
	"os"

	"github.com/lib/pq"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type MigrateServer struct {
	db  *gorm.DB
	log *log.Logger
}

func NewMigrateServer(db *gorm.DB, log *log.Logger) *MigrateServer {
	return &MigrateServer{
		db:  db,
		log: log,
	}
}
func (m *MigrateServer) Start(ctx context.Context) error {
	if err := m.db.AutoMigrate(
		&model.SysUser{},
		&model.SysRole{},
		&model.SysDept{},
		&model.SysMenu{},
		&model.SysLog{},
		&model.SysApi{},
		&model.SysDict{},
		&model.SysConfig{},
		&model.SysTenant{},
		&model.CmsMeta{},
		&model.CmsPost{},
		&model.BusinessCompany{},
		&model.BusinessProduct{},
		&model.BusinessTalent{},
		&model.BusinessExpert{},
		&model.BusinessKnowledge{},
		&model.BusinessApp{},
		&model.BusinessReport{},
		&model.BusinessJob{},
	); err != nil {
		m.log.Error("system migrate error", zap.Error(err))
		return err
	}
	m.log.Info("AutoMigrate success")

	// 创建初始数据
	if err := m.initData(); err != nil {
		m.log.Error("create default data error", zap.Error(err))
		return err
	}
	m.log.Info("Create default data success")

	os.Exit(0)
	return nil
}

func (m *MigrateServer) Stop(ctx context.Context) error {
	m.log.Info("AutoMigrate stop")
	return nil
}

func (m *MigrateServer) initData() error {
	// 创建超级管理员用户
	var count int64
	if err := m.db.Model(&model.SysUser{}).Where("username = ?", "admin").Count(&count).Error; err != nil {
		return err
	}

	// 如果已存在，则不创建
	if count > 0 {
		m.log.Info("Super admin user already exists, skip creation")
		return nil
	}

	// 创建超级管理员角色
	adminRole := &model.SysRole{
		Name:    "超级管理员",
		Code:    "super",
		Summary: "超级管理员，拥有所有权限",
		Status:  true,
	}

	if err := m.db.Create(adminRole).Error; err != nil {
		return err
	}

	// 创建默认部门
	DefaultDept := &model.SysDept{
		Name:    "默认部门",
		Code:    "default",
		Summary: "默认部门，拥有所有权限",
		Status:  true,
	}

	if err := m.db.Create(DefaultDept).Error; err != nil {
		return err
	}

	// 对密码进行加密
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 生成用户ID
	userId, err := sid.NewSid().GenString()
	if err != nil {
		return err
	}

	// 创建默认管理员用户
	adminUser := &model.SysUser{
		UserId:   userId,
		Username: "admin",
		Password: string(hashedPassword),
		Nickname: "超级管理员",
		Email:    "<EMAIL>",
		Status:   true,
		RoleIds:  pq.Int64Array{int64(adminRole.ID)},
		DeptId:   DefaultDept.ID,
	}

	if err := m.db.Create(adminUser).Error; err != nil {
		return err
	}

	return nil
}
