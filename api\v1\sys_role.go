package v1

import "github.com/lib/pq"

type SysRoleCreateParams struct {
	Name    string `json:"name" binding:"required,max=64" example:"管理员"`
	Code    string `json:"code" binding:"required,max=64" example:"admin"`
	Summary string `json:"summary" example:"系统管理员角色"`
	Status  bool   `json:"status" example:"true"`
}

type SysRoleUpdateParams struct {
	SysRoleCreateParams
	Home    string        `json:"home" example:"home"`
	MenuIds pq.Int64Array `json:"menuIds" example:"1,2,3"`
	ApiIds  pq.Int64Array `json:"apiIds" example:"1,2,3"`
}

type SysRoleResponse struct {
	ID        uint          `json:"id"`
	Name      string        `json:"name"`
	Code      string        `json:"code"`
	Summary   string        `json:"summary"`
	Status    bool          `json:"status"`
	Home      string        `json:"home"`
	MenuIds   pq.Int64Array `json:"menuIds"`
	ApiIds    pq.Int64Array `json:"apiIds"`
	CreatedAt string        `json:"createdAt"`
	UpdatedAt string        `json:"updatedAt"`
}
